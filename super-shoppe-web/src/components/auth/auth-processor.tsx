import ErrorInformation from '@components/404/error-information';
import PageLoader from '@components/ui/loaders/page-loader';
import { useTNGAuthMutation } from '@framework/auth/use-tng-auth';
import { User } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { setAuthToken } from '@framework/utils/get-token';
import { getIsTNG, useTNGMy } from '@utils/use-tng';
import { useEffect, useState } from 'react';
import { useQueryClient } from 'react-query';

const AuthProcessor: React.FC<{ auth: User | null }> = ({ auth, children }) => {
  const tngmy = useTNGMy();
  const queryClient = useQueryClient();
  const [isError, setIsError] = useState(false);
  const { mutate: tngUserAuth } = useTNGAuthMutation();

  useEffect(() => {
    if (!auth) {
      if (getIsTNG()) {
        tngmy.postMessage({
          type: 'SS_USER_INFO',
        });

        tngmy.onMessage = function (e: any) {
          if (e.type === 'SS_USER_INFO_COMPLETE' && e.auth_code) {
            tngUserAuth(
              {
                auth_code: e.auth_code,
                authenticate: true,
              },
              {
                onSuccess: (resp_data: any) => {
                  if (resp_data.token) {
                    setAuthToken(resp_data.token);
                  }
                  queryClient.invalidateQueries(API_ENDPOINTS.CART);
                  queryClient.invalidateQueries(API_ENDPOINTS.ME);
                },
                onError: () => {
                  setIsError(true);
                },
              }
            );
          } else {
            setIsError(true);
          }
        };
      } else {
        setIsError(true);
      }
    }
  }, [auth]);

  if (auth) {
    return <>{children}</>;
  }

  // if (isError) {
  //   return <ErrorInformation />;
  // }

  // return <PageLoader />;
  return <> {children}</>;
};

export default AuthProcessor;
