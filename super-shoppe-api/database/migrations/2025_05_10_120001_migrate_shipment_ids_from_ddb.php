<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Database\Models\Order;
use App\Database\Models\OrderShipment;
use App\Database\Models\DDBModel;
use Illuminate\Support\Facades\Log;

class MigrateShipmentIdsFromDdb extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Migrate existing shipment_ids from DynamoDB to order_shipments table
        $this->migrateShipmentIds();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration is not reversible as we're moving data from DDB to MySQL
        // The data will remain in both places for safety
    }

    private function migrateShipmentIds()
    {
        try {
            // Get all DDB records with shipment_id key
            $ddbRecords = DDBModel::where('key', 'shipment_id')->get();
            
            $migratedCount = 0;
            $errorCount = 0;

            foreach ($ddbRecords as $ddbRecord) {
                try {
                    // Extract order_id from uuid (format: orders_123)
                    $uuidParts = explode('_', $ddbRecord->uuid);
                    if (count($uuidParts) !== 2 || $uuidParts[0] !== 'orders') {
                        Log::warning("Invalid DDB UUID format: {$ddbRecord->uuid}");
                        $errorCount++;
                        continue;
                    }

                    $orderId = (int) $uuidParts[1];
                    $shipmentId = $ddbRecord->value;

                    // Check if order exists
                    $order = Order::find($orderId);
                    if (!$order) {
                        Log::warning("Order not found for ID: {$orderId}");
                        $errorCount++;
                        continue;
                    }

                    // Check if shipment_id already exists in order_shipments
                    $existingShipment = OrderShipment::where('order_id', $orderId)
                        ->where('shipment_id', $shipmentId)
                        ->first();

                    if ($existingShipment) {
                        Log::info("Shipment already exists for order {$orderId}, shipment {$shipmentId}");
                        continue;
                    }

                    // Create new order_shipment record
                    OrderShipment::create([
                        'order_id' => $orderId,
                        'shipment_id' => $shipmentId,
                        'status' => 'migrated',
                        'metadata' => [
                            'migrated_from_ddb' => true,
                            'migration_date' => now()->toISOString(),
                            'original_ddb_uuid' => $ddbRecord->uuid
                        ]
                    ]);

                    $migratedCount++;
                    Log::info("Migrated shipment_id {$shipmentId} for order {$orderId}");

                } catch (\Exception $e) {
                    Log::error("Error migrating DDB record {$ddbRecord->uuid}: " . $e->getMessage());
                    $errorCount++;
                }
            }

            Log::info("Shipment ID migration completed. Migrated: {$migratedCount}, Errors: {$errorCount}");

        } catch (\Exception $e) {
            Log::error("Error during shipment ID migration: " . $e->getMessage());
        }
    }
}
