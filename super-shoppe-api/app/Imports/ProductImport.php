<?php

namespace App\Imports;

use App\Database\Models\Branch;
use App\Database\Models\Location;
use App\Database\Models\Product;
use App\Database\Models\User;
use App\Database\Models\Variation;
use App\Enums\ProductType;
use App\Enums\RoleEnum;
use App\Jobs\RecalculateProductPointsJob;
use App\Services\WalletRuleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterImport;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\Type;

class ProductImport extends Import implements ToCollection, ShouldQueue, WithEvents, WithCustomCsvSettings
{
    use RegistersEventListeners;

    protected $cart_redeem_rate = 0;

    public function __construct()
    {
        $spending_rules = app(WalletRuleService::class)->getSpendingRulesInfo();
        $this->cart_redeem_rate = Arr::get($spending_rules, "cart_info.amount", 0.01);
    }

    public function collection($rows)
    {
        $admin_user = User::role(RoleEnum::SUPER_ADMIN)->first();
        Auth::guard('sanctum')->setUser($admin_user);
        Auth::setUser($admin_user);

        $type_id = Type::first()->id;
        $shop_id = Shop::first()->id;
        $branch_id = Branch::first()->id;
        $location_id = Location::first()->id;
        foreach ($rows as $row) {
            try {
                $this->appendMaxRedeemablePointsData($row);
                if(isset($row['id']) && strlen($row['id']) > 0) {
                    $product = $this->getProduct($row['id']);
                    if(isset($row['variant_id']) && strlen($row['variant_id']) > 0) {
                        if($product && $product->product_type == ProductType::VARIABLE) {
                            $variant = $product->variation_options()->find($row['variant_id']);
                            $this->updateProductVariant($variant, $row);
                        }
                    }
                    else {
                        $this->updateProduct($product, $row);
                    }
                }
                else {
                    $this->createProduct(array_merge($row->toArray(), [
                        'type_id' => $type_id,
                        'shop_id' => $shop_id,
                        'branch_id' => $branch_id,
                        'location_id' => $location_id,
                    ]));
                }
            }
            catch (\Exception $e) {
                info('ImportProductException', ['message' => $e->getMessage()]);
                info('ImportProductFailed', ['data' => $row]);
            }
        }
    }

    protected function createProduct($data)
    {
        $id = Arr::get($data, 'id');
        $name = Arr::get($data, 'name');
        $description = Arr::get($data, 'description');
        $sku = Arr::get($data, 'sku');
        $quantity = Arr::get($data, 'quantity', 0);
        $price = Arr::get($data, 'price');
        $sale_price = Arr::get($data, 'sale_price');
        $max_redeemable_points = Arr::get($data, 'max_redeemable_points', 0);
        $height = Arr::get($data, 'height');
        $length = Arr::get($data, 'length');
        $width = Arr::get($data, 'width');
        $weight = Arr::get($data, 'weight');
        $status = Arr::get($data, 'status', 'draft');
        $shop_id = Arr::get($data, 'shop_id');
        $type_id = Arr::get($data, 'type_id');
        $branch_id = Arr::get($data, 'branch_id');
        $location_id = Arr::get($data, 'location_id');
        $product_type = 'simple';
        $image = [];
        $gallery = [];
        $meta = [
            'seo_title' => null,
            'seo_keywords' => null,
            'show_discount' => true,
            'show_quantity' => true,
            'seo_description' => null
        ];
        $product = Product::create([
            'id' => $id,
            'name' => $name,
            'description' => $description,
            'sku' => $sku,
            'quantity' => $quantity,
            'price' => $price,
            'sale_price' => $sale_price,
            'max_redeemable_points' => $max_redeemable_points,
            'height' => $height,
            'length' => $length,
            'width' => $width,
            'weight' => $weight,
            'status' => $status,
            'shop_id' => $shop_id,
            'type_id' => $type_id,
            'product_type' => $product_type,
            'meta' => $meta,
            'image' => $image,
            'gallery' => $gallery,
        ]);
        $inventory_data = [
            'quantity' => $quantity,
        ];
        $inventory = $product->inventory()->create($inventory_data);
        $inventory->locations()->sync([
            $location_id => [
                'label' => null,
                'quantity' => $quantity
            ]
        ]);
        $product->branch()->attach($branch_id);
    }

    protected function updateProduct(Product $product, $data)
    {
        if($product) {
            $this->updateResourceData($product, $data, 'name', '');
            $this->updateResourceData($product, $data, 'description', '');
            $this->updateResourceData($product, $data, 'sku', null);
            $this->updateResourceData($product, $data, 'price', 0);
            $this->updateResourceData($product, $data, 'sale_price', null);
            $this->updateResourceData($product, $data, 'max_redeemable_points', null);
            $this->updateResourceData($product, $data, 'height', null);
            $this->updateResourceData($product, $data, 'length', null);
            $this->updateResourceData($product, $data, 'width', null);
            $this->updateResourceData($product, $data, 'weight', null);
            $this->updateResourceData($product, $data, 'status', 'inactive');
            if($product->product_type != ProductType::VARIABLE) {
                if(isset($data['quantity']) && strlen($data['quantity']) > 0) {
                    if(isset($product->inventory)) {
                        $product->inventory()->update(['quantity' => $data['quantity']]);
                    } else {
                        $product->inventory()->create(['quantity' => $data['quantity']]);
                    }
                }
            }
            $product->save();
        }
    }

    protected function updateProductVariant(Variation $variant, $data)
    {
        if($variant) {
            $this->updateResourceData($variant, $data, 'name', '', 'title');
            $this->updateResourceData($variant, $data, 'sku', null);
            $this->updateResourceData($variant, $data, 'price', 0);
            $this->updateResourceData($variant, $data, 'sale_price', null);
            $this->updateResourceData($variant, $data, 'max_redeemable_points', null);
            $this->updateResourceData($variant, $data, 'height', null);
            $this->updateResourceData($variant, $data, 'length', null);
            $this->updateResourceData($variant, $data, 'width', null);
            $this->updateResourceData($variant, $data, 'weight', null);
            $variant->is_disable = (!isset($data['status']) || strlen($data['status']) <= 0 || $data['status'] === 'disabled');
            if(isset($data['quantity']) && strlen($data['quantity']) > 0) {
                if(isset($variant->inventory)) {
                    $variant->inventory()->update(['quantity' => $data['quantity']]);
                } else {
                    $variant->inventory()->create(['quantity' => $data['quantity']]);
                }
            }
            $variant->save();
        }
    }

    protected function updateResourceData(&$resource, $data, $key, $value_if_empty, $resolve_to_key = null)
    {
        $resource->{$resolve_to_key ?: $key} = (isset($data[$key]) && strlen($data[$key]) > 0) ? $data[$key] : $value_if_empty;
    }

    protected function getProduct($id)
    {
        return Product::withoutGlobalScopes()->withTrashed(false)->find($id);
    }

    protected function appendMaxRedeemablePointsData(&$data)
    {
        $price = Arr::get($data, 'price');
        $sale_price = Arr::get($data, 'sale_price');

        $max_redeemable_points = $sale_price ? (($price - $sale_price) / $this->cart_redeem_rate) : null;

        $data['max_redeemable_points'] = $max_redeemable_points > 0 ? round($max_redeemable_points, 0) : null;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public static function afterImport(AfterImport $event)
    {
        // dispatch recalculate points job after import completed
        RecalculateProductPointsJob::dispatch(1, 100);
    }

    public function getCsvSettings(): array
    {
        return [
            'delimiter' => ','
        ];
    }
}