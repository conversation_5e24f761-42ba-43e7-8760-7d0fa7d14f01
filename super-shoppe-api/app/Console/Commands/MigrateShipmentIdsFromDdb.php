<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Database\Models\Order;
use App\Database\Models\OrderShipment;
use App\Database\Models\DDBModel;
use Illuminate\Support\Facades\Log;

class MigrateShipmentIdsFromDdb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipments:migrate-from-ddb 
                            {--batch-size=50 : Number of records to process in each batch}
                            {--delay=0.5 : Delay in seconds between each DDB fetch}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate shipment IDs from DynamoDB to order_shipments table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $delay = (float) $this->option('delay');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $this->info('Starting migration of shipment IDs from DynamoDB...');
        $this->info("Batch size: {$batchSize}, Delay: {$delay}s");

        try {
            $migratedCount = 0;
            $errorCount = 0;
            $skippedCount = 0;
            $processedCount = 0;

            // First, get all orders from MySQL
            $this->info('Fetching all orders from database...');
            $orders = Order::all();

            if ($orders->isEmpty()) {
                $this->info('No orders found in database');
                return 0;
            }

            $totalOrders = count($orders);
            $this->info("Found {$totalOrders} orders in database");

            // Process each order individually with delay
            foreach ($orders as $index => $order) {
                $orderNumber = $index + 1;

                // Add delay between each DynamoDB query (except for first query)
                if ($index > 0) {
                    usleep($delay * 1000000); // 0.5 second delay between each DDB fetch
                }

                try {
                    $this->info("Processing order {$orderNumber}/{$totalOrders}: Order ID {$order->id}");

                    // Query DynamoDB for this specific order's shipment_id
                    // The uuid format is: orders_{order_id}
                    $uuid = 'orders_' . $order->id;
                    $ddbRecords = DDBModel::where('uuid', $uuid)
                        ->where('key', 'shipment_id')
                        ->get();

                    if ($ddbRecords->isEmpty()) {
                        $this->info("- No shipment_id found for Order ID {$order->id}");
                        $processedCount++;
                        continue;
                    }

                    // Process each shipment record for this order
                    foreach ($ddbRecords as $ddbRecord) {
                        $result = $this->processDdbRecordForOrder($ddbRecord, $order, $dryRun);

                        switch ($result['status']) {
                            case 'migrated':
                                $migratedCount++;
                                $this->info("✓ Migrated: Order {$order->id} -> Shipment {$ddbRecord->value}");
                                break;
                            case 'skipped':
                                $skippedCount++;
                                $this->info("- Skipped: Order {$order->id} -> Shipment {$ddbRecord->value} (already exists)");
                                break;
                            case 'error':
                                $errorCount++;
                                $this->error("✗ Error: Order {$order->id} - {$result['message']}");
                                break;
                        }
                    }

                    $processedCount++;

                    // Show progress summary every 10 orders
                    if ($processedCount % 10 === 0) {
                        $this->info("Progress: {$processedCount}/{$totalOrders} orders | Migrated: {$migratedCount} | Skipped: {$skippedCount} | Errors: {$errorCount}");
                    }
                } catch (\Exception $e) {
                    $this->error("Error processing Order ID {$order->id}: " . $e->getMessage());
                    $errorCount++;
                    $processedCount++;
                }
            }

            $this->newLine();

            // Summary
            $this->info('Migration completed!');
            $this->table(
                ['Status', 'Count'],
                [
                    ['Migrated', $migratedCount],
                    ['Skipped (already exists)', $skippedCount],
                    ['Errors', $errorCount],
                    ['Total processed', $processedCount]
                ]
            );

            if ($dryRun) {
                $this->warn('This was a DRY RUN - no actual changes were made');
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Fatal error during migration: " . $e->getMessage());
            Log::error("Fatal error during shipment ID migration: " . $e->getMessage());
            return 1;
        }
    }

    private function processDdbRecordForOrder($ddbRecord, $order, $dryRun = false)
    {
        $shipmentId = $ddbRecord->value;

        // Check if shipment_id already exists in order_shipments
        $existingShipment = OrderShipment::where('order_id', $order->id)
            ->where('shipment_id', $shipmentId)
            ->first();

        if ($existingShipment) {
            return ['status' => 'skipped', 'message' => 'Already exists'];
        }

        if (!$dryRun) {
            // Create new order_shipment record
            OrderShipment::create([
                'order_id' => $order->id,
                'shipment_id' => $shipmentId,
                'metadata' => [
                    'migrated_from_ddb' => true,
                    'migration_date' => now()->toISOString(),
                    'original_ddb_uuid' => $ddbRecord->uuid
                ]
            ]);

            Log::info("Migrated shipment_id {$shipmentId} for order {$order->id}");
        }

        return ['status' => 'migrated', 'message' => 'Successfully migrated'];
    }
}
