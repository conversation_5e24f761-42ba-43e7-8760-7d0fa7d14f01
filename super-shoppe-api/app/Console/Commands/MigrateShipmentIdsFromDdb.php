<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Database\Models\Order;
use App\Database\Models\OrderShipment;
use App\Database\Models\DDBModel;
use Illuminate\Support\Facades\Log;

class MigrateShipmentIdsFromDdb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipments:migrate-from-ddb 
                            {--batch-size=50 : Number of records to process in each batch}
                            {--delay=0.5 : Delay in seconds between each DDB fetch}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate shipment IDs from DynamoDB to order_shipments table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $delay = (float) $this->option('delay');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $this->info('Starting migration of shipment IDs from DynamoDB...');
        $this->info("Batch size: {$batchSize}, Delay: {$delay}s");

        try {
            // Get total count first
            $totalRecords = DDBModel::where('key', 'shipment_id')->count();
            $this->info("Found {$totalRecords} shipment_id records in DynamoDB");

            if ($totalRecords === 0) {
                $this->info('No shipment_id records found in DynamoDB');
                return 0;
            }

            $migratedCount = 0;
            $errorCount = 0;
            $skippedCount = 0;
            $offset = 0;

            // Create progress bar
            $progressBar = $this->output->createProgressBar($totalRecords);
            $progressBar->start();

            while ($offset < $totalRecords) {
                // Fetch batch with delay to respect DDB throttling
                if ($offset > 0) {
                    usleep($delay * 1000000); // Convert to microseconds
                }

                $ddbRecords = DDBModel::where('key', 'shipment_id')
                    ->skip($offset)
                    ->take($batchSize)
                    ->get();

                if ($ddbRecords->isEmpty()) {
                    break;
                }

                foreach ($ddbRecords as $ddbRecord) {
                    try {
                        $result = $this->processDdbRecord($ddbRecord, $dryRun);

                        switch ($result['status']) {
                            case 'migrated':
                                $migratedCount++;
                                break;
                            case 'skipped':
                                $skippedCount++;
                                break;
                            case 'error':
                                $errorCount++;
                                break;
                        }

                        $progressBar->advance();
                    } catch (\Exception $e) {
                        $this->error("Error processing DDB record {$ddbRecord->uuid}: " . $e->getMessage());
                        $errorCount++;
                        $progressBar->advance();
                    }
                }

                $offset += $batchSize;
            }

            $progressBar->finish();
            $this->newLine(2);

            // Summary
            $this->info('Migration completed!');
            $this->table(
                ['Status', 'Count'],
                [
                    ['Migrated', $migratedCount],
                    ['Skipped (already exists)', $skippedCount],
                    ['Errors', $errorCount],
                    ['Total processed', $migratedCount + $skippedCount + $errorCount]
                ]
            );

            if ($dryRun) {
                $this->warn('This was a DRY RUN - no actual changes were made');
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Fatal error during migration: " . $e->getMessage());
            Log::error("Fatal error during shipment ID migration: " . $e->getMessage());
            return 1;
        }
    }

    private function processDdbRecord($ddbRecord, $dryRun = false)
    {
        // Extract order_id from uuid (format: orders_123)
        $uuidParts = explode('_', $ddbRecord->uuid);
        if (count($uuidParts) !== 2 || $uuidParts[0] !== 'orders') {
            $this->warn("Invalid DDB UUID format: {$ddbRecord->uuid}");
            return ['status' => 'error', 'message' => 'Invalid UUID format'];
        }

        $orderId = (int) $uuidParts[1];
        $shipmentId = $ddbRecord->value;

        // Check if order exists
        $order = Order::find($orderId);
        if (!$order) {
            $this->warn("Order not found for ID: {$orderId}");
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // Check if shipment_id already exists in order_shipments
        $existingShipment = OrderShipment::where('order_id', $orderId)
            ->where('shipment_id', $shipmentId)
            ->first();

        if ($existingShipment) {
            return ['status' => 'skipped', 'message' => 'Already exists'];
        }

        if (!$dryRun) {
            // Create new order_shipment record
            OrderShipment::create([
                'order_id' => $orderId,
                'shipment_id' => $shipmentId,
                'metadata' => [
                    'migrated_from_ddb' => true,
                    'migration_date' => now()->toISOString(),
                    'original_ddb_uuid' => $ddbRecord->uuid
                ]
            ]);

            Log::info("Migrated shipment_id {$shipmentId} for order {$orderId}");
        }

        return ['status' => 'migrated', 'message' => 'Successfully migrated'];
    }
}
