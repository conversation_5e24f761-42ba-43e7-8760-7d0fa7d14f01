<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Database\Models\Order;
use App\Database\Models\OrderShipment;
use App\Database\Models\DDBModel;
use Illuminate\Support\Facades\Log;

class MigrateShipmentIdsFromDdb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipments:migrate-from-ddb 
                            {--batch-size=50 : Number of records to process in each batch}
                            {--delay=0.5 : Delay in seconds between each DDB fetch}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate shipment IDs from DynamoDB to order_shipments table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $delay = (float) $this->option('delay');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $this->info('Starting migration of shipment IDs from DynamoDB...');
        $this->info("Batch size: {$batchSize}, Delay: {$delay}s");

        try {
            $migratedCount = 0;
            $errorCount = 0;
            $skippedCount = 0;
            $processedCount = 0;

            // Get all records with key = 'shipment_id' using proper DynamoDB query
            $this->info('Fetching all shipment_id records from DynamoDB using key_value_index...');

            // Add initial delay before first query
            $this->info("Waiting {$delay}s before querying DynamoDB...");
            usleep($delay * 1000000);

            $ddbRecords = DDBModel::query()->withIndex('key_value_index')
                ->where('key', 'shipment_id')
                ->get();

            if ($ddbRecords->isEmpty()) {
                $this->info('No shipment_id records found in DynamoDB');
                return 0;
            }

            $totalRecords = count($ddbRecords);
            $this->info("Found {$totalRecords} shipment_id records in DynamoDB");

            // Process each record individually with delay
            foreach ($ddbRecords as $index => $ddbRecord) {
                $recordNumber = $index + 1;

                // Add delay between each record processing (except for first record)
                if ($index > 0) {
                    usleep($delay * 1000000); // 0.5 second delay between each fetch
                }

                try {
                    $this->info("Processing record {$recordNumber}/{$totalRecords}: {$ddbRecord->uuid}");

                    $result = $this->processDdbRecord($ddbRecord, $dryRun);

                    switch ($result['status']) {
                        case 'migrated':
                            $migratedCount++;
                            $this->info("✓ Migrated: {$ddbRecord->value}");
                            break;
                        case 'skipped':
                            $skippedCount++;
                            $this->info("- Skipped: {$ddbRecord->value} (already exists)");
                            break;
                        case 'error':
                            $errorCount++;
                            $this->error("✗ Error: {$ddbRecord->value} - {$result['message']}");
                            break;
                    }

                    $processedCount++;

                    // Show progress summary every 10 records
                    if ($processedCount % 10 === 0) {
                        $this->info("Progress: {$processedCount}/{$totalRecords} | Migrated: {$migratedCount} | Skipped: {$skippedCount} | Errors: {$errorCount}");
                    }
                } catch (\Exception $e) {
                    $this->error("Error processing DDB record {$ddbRecord->uuid}: " . $e->getMessage());
                    $errorCount++;
                    $processedCount++;
                }
            }

            $this->newLine();

            // Summary
            $this->info('Migration completed!');
            $this->table(
                ['Status', 'Count'],
                [
                    ['Migrated', $migratedCount],
                    ['Skipped (already exists)', $skippedCount],
                    ['Errors', $errorCount],
                    ['Total processed', $processedCount]
                ]
            );

            if ($dryRun) {
                $this->warn('This was a DRY RUN - no actual changes were made');
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Fatal error during migration: " . $e->getMessage());
            Log::error("Fatal error during shipment ID migration: " . $e->getMessage());
            return 1;
        }
    }

    private function processDdbRecord($ddbRecord, $dryRun = false)
    {
        // Extract order_id from uuid (format: orders_123)
        $uuidParts = explode('_', $ddbRecord->uuid);
        if (count($uuidParts) !== 2 || $uuidParts[0] !== 'orders') {
            $this->warn("Invalid DDB UUID format: {$ddbRecord->uuid}");
            return ['status' => 'error', 'message' => 'Invalid UUID format'];
        }

        $orderId = (int) $uuidParts[1];
        $shipmentId = $ddbRecord->value;

        // Check if order exists
        $order = Order::find($orderId);
        if (!$order) {
            $this->warn("Order not found for ID: {$orderId}");
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // Check if shipment_id already exists in order_shipments
        $existingShipment = OrderShipment::where('order_id', $orderId)
            ->where('shipment_id', $shipmentId)
            ->first();

        if ($existingShipment) {
            return ['status' => 'skipped', 'message' => 'Already exists'];
        }

        if (!$dryRun) {
            // Create new order_shipment record
            OrderShipment::create([
                'order_id' => $orderId,
                'shipment_id' => $shipmentId,
                'metadata' => [
                    'migrated_from_ddb' => true,
                    'migration_date' => now()->toISOString(),
                    'original_ddb_uuid' => $ddbRecord->uuid
                ]
            ]);

            Log::info("Migrated shipment_id {$shipmentId} for order {$orderId}");
        }

        return ['status' => 'migrated', 'message' => 'Successfully migrated'];
    }
}
