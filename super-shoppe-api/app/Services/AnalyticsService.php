<?php

namespace App\Services;

use App\Enums\ExportEnum;
use App\Enums\OrderEnum;
use App\Enums\OrderTypeEnum;
use App\Helper\ExportHelper;
use App\Database\Models\DDBModel;
use App\Database\Repositories\OrderRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class AnalyticsService
{
    protected OrderRepository $orderRepository;

    public function __construct(OrderRepository $orderRepository)
    {
        $this->orderRepository = $orderRepository;
    }

    public function getOrderAnalytics(array $input): LengthAwarePaginator
    {
        $query = $this->getOrderAnalyticsQuery($input);
        $limit = Arr::get($input, 'limit', 20);
        $page = Arr::get($input, 'page', 20);
        return $query->paginate($limit, ['*'], 'paginate', $page);
    }

    public function exportOrderAnalytics(array $options)
    {
        $exportHelper = new ExportHelper(ExportEnum::ORDER_ANALYTICS, "exports");

        $options = array_merge($options ?? [], ['format' => 'xlsx']);

        try {
            $result = $exportHelper->export($options);
            if ($result) {
                return [
                    'success' => true,
                    'url' => $result
                ];
            } else {
                return [
                    'success' => false
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception($e);
        }
    }

    public function getOrderAnalyticsQuery(array $options)
    {
        $query = $this->orderRepository->with([
            'products',
            'payments',
            'order_status',
        ]);

        $options = $options ?: [];

        if (is_null(Arr::get($options, 'branch_id'))) {
            $query->withoutBranch();
        }

        if ($order_number = Arr::get($options, 'order_number')) {
            $query->whereRaw('display_id LIKE ?', ["%{$order_number}%"]);
        }

        if ($tracking_number = Arr::get($options, 'tracking_number')) {
            $data = DDBModel::query()->withIndex('key_value_index')
                ->where('key', 'shipment_id')
                ->whereRaw('value LIKE ?', ["%{$tracking_number}%"])
                ->get();

            $orderIds = $data->pluck('uuid')->map(function ($uuid) {
                return Str::remove('orders_', $uuid);
            });

            $query->whereIn('id', $orderIds);
        }

        $payment_from = Arr::get($options, 'payment_from');
        $payment_to = Arr::get($options, 'payment_to');

        if ($payment_from || $payment_to) {
            $query->whereHas('payments', function ($q) use ($payment_from, $payment_to) {
                $q->when($payment_from, fn($q, $date) => $q->where('payment_transactions.created_at', '>', Carbon::parse($date)->format('Y-m-d 00:00:00')))
                    ->when($payment_to, fn($q, $date) => $q->where('payment_transactions.created_at', '<=', Carbon::parse($date)->format('Y-m-d 23:59:59')));
            });
        }

        $status_slug = null;
        $completed_from = Arr::get($options, 'completed_from');
        $completed_to = Arr::get($options, 'completed_to');

        if ($completed_from || $completed_to) {
            $query->when($completed_from, fn($q, $date) => $q->where('orders.updated_at', '>', Carbon::parse($date)->format('Y-m-d 00:00:00')))
                ->when($completed_to, fn($q, $date) => $q->where('orders.updated_at', '<=', Carbon::parse($date)->format('Y-m-d 23:59:59')));

            $status_slug = OrderEnum::COMPLETE;
        }

        if ($delivery_status = Arr::get($options, 'delivery_status')) {
            $query->whereHas('order_status', fn($q) => $q->whereIn('slug', mapDeliveryStatusToOrderStatus($delivery_status)));
        }

        if ($status_slug || $status_slug = Arr::get($options, 'order_status')) {
            $query->whereRelation('order_status', 'slug', '=', $status_slug);
        } else {
            $query->whereRelation('order_status', 'slug', '!=', OrderEnum::CANCELED);
        }

        $orderBy = Arr::get($options, 'order_by', 'id');
        $order = Arr::get($options, 'order', 'DESC');

        $query->orderBy($orderBy, $order);
        dd($query->toSql());
        return $query;
    }
}
