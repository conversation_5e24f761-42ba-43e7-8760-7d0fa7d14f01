<?php

namespace App\Services\Api;

use App\Database\Models\Cart;
use App\Database\Models\Order;
use App\Database\Models\OrderShipment;
use App\Database\Models\Settings;
use App\Database\Models\Shipping;
use App\Enums\ProductType;
use App\Helper\APIHelper;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class DelyvaService
{
    public function getShipmentOrigin()
    {
        $settings = Settings::first();
        $shipment_origin = isset($settings['options']['shipment_origin']) ? $settings['options']['shipment_origin'] : [];
        return $shipment_origin;
    }

    public function getShipmentSenderContact()
    {
        $settings = Settings::first();
        $sender_contact = isset($settings['options']['shipment_sender_contact'])
            ? $settings['options']['shipment_sender_contact']
            : [
                'name' => 'TS Home Signature',
                'email' => '<EMAIL>',
                'phone' => '0192221818',
                'unitNo' => 'A-1-1, Block A',
            ];

        return $sender_contact;
    }

    public function getQuote(Cart $cart, Shipping $shipping_method)
    {
        if (!$cart->shipping_address) {
            return [
                'status' => 400,
                'message' => 'Please enter shipping address first'
            ];
        }

        $url = config('services.delyva.url') . '/service/instantQuote';

        // $cartDimension = $this->calculateCartDimension($cart);

        $body = [
            'customerId' => config('services.delyva.customer_id'),
            'origin' => Arr::get($shipping_method->pickup_details ?? [], 'shipment_origin') ?: $this->getShipmentOrigin(),
            'destination' => [
                'address1' => Arr::get($cart, 'shipping_address.address.street_address'),
                'address2' => '',
                'city' => Arr::get($cart, 'shipping_address.address.city'),
                'state' => Arr::get($cart, 'shipping_address.address.state'),
                'postcode' => Arr::get($cart, 'shipping_address.address.zip'),
                'country' => 'MY',
            ],
            'inventory' => [],
            // 'weight' => [
            //     'unit' => 'kg',
            //     'value' => $cartDimension['weight'],
            // ],
        ];

        // if ($cartDimension['height'] && $cartDimension['length'] && $cartDimension['width']) {
        //     $body['dimension'] = [
        //         'width' => $cartDimension['width'],
        //         'length' => $cartDimension['length'],
        //         'height' => $cartDimension['height'],
        //         'unit' => 'cm'
        //     ];
        // }

        if ($shipping_method && $shipping_method->code) {
            $body['serviceType'] = $shipping_method->code;
        }

        foreach ($cart->items()->where('product_type', '!=', ProductType::VOUCHER)->wherePivot('is_checkout', true)->get() as $cart_item) {
            $item = $cart_item->pivot->variation_option_id ? $cart_item->variation_options()->firstWhere('id', $cart_item->pivot->variation_option_id) : $cart_item;
            $item_weight = $item ? ($item->weight ?: 0) : 0;
            $item_length = $item ? ($item->length ?: 0) : 0;
            $item_height = $item ? ($item->height ?: 0) : 0;
            $item_width = $item ? ($item->width ?: 0) : 0;
            $item_name = $cart_item->pivot->variation_option_id ? $item->title : $item->name;

            $data = [
                'name' => $item_name,
                'type' => 'PARCEL',
                'price' => [
                    'amount' => $cart_item->pivot->price,
                    'currency' => 'MYR'
                ],
                'weight' => [
                    'value' => $item_weight,
                    'unit' => 'kg'
                ],
                'dimension' => [
                    'unit' => 'cm',
                    'width' => $item_width,
                    'length' => $item_length,
                    'height' => $item_height,
                ],
                'quantity' => $cart_item->pivot->quantity
            ];

            $body['inventory'][] = $data;
        }

        try {
            $resp = APIHelper::request('POST', $url, $this->dataMap($body));
        } catch (\Exception $e) {
            Log::info('Delyva@getCartQuote', [
                'url' => $url,
                'body' => $body,
                'message' => $e->getMessage()
            ]);
            return [
                'status' => 400,
                'message' => 'Failed to get shipping details, please check your shipping address or items weight have exceeded service quota.'
            ];
        }

        if (env('LOG_DELYVA_REQUEST')) {
            Log::info('Delyva@getCartQuoteRequest', [
                'url' => $url,
                'body' => $body,
            ]);
        }

        $service = Arr::get($resp, 'data.data.services.0');
        if (!$service) {
            return [
                'status' => 400,
                'message' => 'No shipping method available'
            ];
        }

        return [
            'status' => 200,
            'price' => $service['price']['amount'],
            'service_code' => $service['service']['code']
        ];
    }

    public function createShipment(Order $order)
    {
        $url = config('services.delyva.url') . '/order';
        $quote = $this->getOrderQuote($order);

        if ($quote['status'] != 200) {
            abort(400, 'Shipment cant be quoted, errors:' . json_encode($quote['message']));
        }

        $serviceCode = $quote['service_code'];
        $date = now();

        $body = [
            'customerId' => config('services.delyva.customer_id'),
            'process' => true,
            'serviceCode' => $serviceCode,
            'origin' => [
                'scheduledAt' => $date,
                'contact' => array_merge(
                    Arr::get($order->shipping_method->pickup_details ?? [], 'shipment_origin') ?: $this->getShipmentSenderContact(),
                    Arr::get($order->shipping_method->pickup_details ?? [], 'shipment_sender_contact') ?: $this->getShipmentOrigin()
                ),
                'inventory' => []
            ],
            'destination' => [
                'inventory' => [],
                'contact' => [
                    'name' => Arr::get($order, 'shipping_address.recipient'),
                    'email' => Arr::get($order, 'shipping_address.email'),
                    'phone' => Arr::get($order, 'shipping_address.contact_number'),
                    'mobile' => Arr::get($order, 'shipping_address.contact_number'),
                    'unitNo' => '',
                    'address1' => Arr::get($order, 'shipping_address.street_address'),
                    'address2' => '',
                    'city' => Arr::get($order, 'shipping_address.city'),
                    'state' => Arr::get($order, 'shipping_address.state'),
                    'postcode' => Arr::get($order, 'shipping_address.zip'),
                    'country' => 'MY',
                ],
            ],
        ];

        if ($order->delivery_remarks) {
            $body['note'] = $order->delivery_remarks;
        }

        foreach ($order->products as $orderProduct) {
            $data = [
                'name' => $orderProduct->name,
                'type' => 'PARCEL',
                'price' => [
                    'amount' => $orderProduct->unit_price,
                    'currency' => 'MYR'
                ],
                'weight' => [
                    'value' => $orderProduct->weight,
                    'unit' => 'kg'
                ],
                'quantity' => $orderProduct->order_quantity
            ];

            if ($orderProduct->height && $orderProduct->length && $orderProduct->width) {
                $data['dimension'] = [
                    'width' => $orderProduct->width,
                    'length' => $orderProduct->length,
                    'height' => $orderProduct->height,
                    'unit' => 'cm'
                ];
            }

            $body['origin']['inventory'][] = $data;
            $body['destination']['inventory'][] = $data;
        }


        $resp = $this->createOrder($body);

        $shipOrderId = Arr::get($resp, 'data.data.orderId');

        if (!$shipOrderId) {
            Log::info('Delyva@getOrderQuote', [
                'url' => $url,
                'order_id' => $order->id,
                'body' => $body,
                'resp' => $resp,
                'message' => 'Shipment cant be created'
            ]);

            abort(400, 'Shipment cant be created, errors:' . json_encode($resp));
        }

        // Create new order shipment record
        OrderShipment::create([
            'order_id' => $order->id,
            'shipment_id' => $shipOrderId,
            'metadata' => [
                'delyva_response' => $resp,
                'service_code' => $serviceCode,
                'created_at' => now()->toISOString()
            ]
        ]);
    }

    public function createOrder($body)
    {
        $url = config('services.delyva.url') . '/order';
        Log::info('DelyvaCreateShipment', $body);
        $resp = APIHelper::request('POST', $url, $this->dataMap($body));

        return $resp;
    }

    public function getAirwayBillURL(Order $order, $specific_shipment_id = null)
    {
        if ($specific_shipment_id) {
            // Return URL for specific shipment
            return config('services.delyva.url') . '/order/' . $specific_shipment_id . '/label?companyId=' . config('services.delyva.company_id');
        }

        // Return URLs for all shipments
        $urls = [];
        foreach ($order->order_shipments as $orderShipment) {
            $urls[] = [
                'shipment_id' => $orderShipment->shipment_id,
                'url' => config('services.delyva.url') . '/order/' . $orderShipment->shipment_id . '/label?companyId=' . config('services.delyva.company_id'),
                'created_at' => $orderShipment->created_at
            ];
        }

        // For backward compatibility, if only one shipment exists, return just the URL string
        if (count($urls) === 1) {
            return $urls[0]['url'];
        }

        return $urls;
    }

    private function getOrderQuote(Order $order)
    {
        $url = config('services.delyva.url') . '/service/instantQuote';

        // $orderDimension = $this->calculateOrderDimension($order);

        $body = [
            'customerId' => config('services.delyva.customer_id'),
            'origin' => Arr::get($order->shipping_method->pickup_details ?? [], 'shipment_origin') ?: $this->getShipmentOrigin(),
            'destination' => [
                'address1' => Arr::get($order, 'shipping_address.street_address'),
                'address2' => '',
                'city' => Arr::get($order, 'shipping_address.city'),
                'state' => Arr::get($order, 'shipping_address.state'),
                'postcode' => Arr::get($order, 'shipping_address.zip'),
                'country' => 'MY',
            ],
            'inventory' => [],
            // 'weight' => [
            //     'unit' => 'kg',
            //     'value' => $orderDimension['weight'],
            // ],
        ];

        // if ($orderDimension['height'] && $orderDimension['length'] && $orderDimension['width']) {
        //     $body['dimension'] = [
        //         'width' => $orderDimension['width'],
        //         'length' => $orderDimension['length'],
        //         'height' => $orderDimension['height'],
        //         'unit' => 'cm'
        //     ];
        // }

        $shipping_method = $order->shipping_method;

        if ($shipping_method && $shipping_method->code) {
            $body['serviceType'] = $shipping_method->code;
        }

        foreach ($order->products as $orderProduct) {
            $data = [
                'name' => $orderProduct->name,
                'type' => 'PARCEL',
                'price' => [
                    'amount' => $orderProduct->unit_price,
                    'currency' => 'MYR'
                ],
                'weight' => [
                    'value' => $orderProduct->weight,
                    'unit' => 'kg'
                ],
                'quantity' => $orderProduct->order_quantity
            ];

            if ($orderProduct->height && $orderProduct->length && $orderProduct->width) {
                $data['dimension'] = [
                    'width' => $orderProduct->width,
                    'length' => $orderProduct->length,
                    'height' => $orderProduct->height,
                    'unit' => 'cm'
                ];
            }

            $body['inventory'][] = $data;
        }

        $resp = APIHelper::request('POST', $url, $this->dataMap($body));

        Log::info('Delyva@getOrderQuote', [
            'url' => $url,
            'body' => $body,
            'resp' => $resp
        ]);

        $service = Arr::get($resp, 'data.data.services.0');

        if (!$service) {
            Log::info('Delyva@getOrderQuote', [
                'url' => $url,
                'order_id' => $order->id,
                'body' => $body,
                'resp' => $resp,
                'message' => 'No shipping method available'
            ]);

            return [
                'status' => 400,
                'message' => 'No shipping method available'
            ];
        }

        return [
            'status' => 200,
            'price' => $service['price']['amount'],
            'service_code' => $service['service']['code']
        ];
    }

    private function calculateCartDimension(Cart $cart)
    {
        $total_weight = 0;
        $total_height = 0;
        $total_width = 0;
        $total_length = 0;

        foreach ($cart->items()->where('product_type', '!=', ProductType::VOUCHER)->wherePivot('is_checkout', true)->get() as $cart_item) {
            $item = $cart_item->pivot->variation_option_id ? $cart_item->variation_options()->firstWhere('id', $cart_item->pivot->variation_option_id) : $cart_item;
            $item_weight = $item ? ($item->weight ?: 0) : 0;

            $total_height += $item->height * $cart_item->pivot->quantity;
            $total_width += $item->width * $cart_item->pivot->quantity;
            $total_length += $item->length * $cart_item->pivot->quantity;
            $total_weight += $item_weight * $cart_item->pivot->quantity;
        }

        return [
            'weight' => $total_weight,
            'height' => $total_height,
            'width' => $total_width,
            'length' => $total_length
        ];
    }

    public function calculateOrderDimension(Order $order)
    {
        $total_weight = 0;
        $total_height = 0;
        $total_width = 0;
        $total_length = 0;

        foreach ($order->products as $product) {
            $qty = $product->order_quantity;
            $total_height += $product->height * $qty;
            $total_width += $product->width * $qty;
            $total_length += $product->length * $qty;
            $total_weight += $product->weight * $qty;
        }

        return [
            'weight' => $total_weight,
            'height' => $total_height,
            'width' => $total_width,
            'length' => $total_length
        ];
    }

    private function dataMap($body)
    {
        return [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Delyvax-Access-Token' => config('services.delyva.api_key')
            ],
            'json' => $body
        ];
    }
}
