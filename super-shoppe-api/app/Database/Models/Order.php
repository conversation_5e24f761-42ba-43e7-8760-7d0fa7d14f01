<?php

namespace App\Database\Models;

use App\Enums\CreditMemoEnum;
use App\Enums\OrderEnum;
use App\Enums\PaymentTransactionEnum;
use App\Traits\HasBranch;
use App\Traits\HasDDBData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Marvel\Database\Models\Coupon;
use Marvel\Database\Models\OrderStatus;
use Marvel\Database\Models\Refund;
use Marvel\Database\Models\Shop;

class Order extends Model
{
    use HasBranch, SoftDeletes;

    use HasDDBData {
        HasDDBData::_fill as fill;
    }

    protected $fill_values = [];

    public static $ID_PREFIX = 'MY';

    protected $table = 'orders';

    public $guarded = [];

    protected $appends = [
        'is_invoiced',
        'qty_ordered',
        'qty_shipped',
        'refunded_shipping',
        'can_cancel',
    ];

    protected $casts = [
        'shipping_address' => 'json',
        'billing_address' => 'json',
    ];

    protected $with = [];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($order) {
            $order->display_id = self::$ID_PREFIX . (10000000 + $order->id);
            $order->saveQuietly();

            $branch_id = Branch::getId();
            $order->branch()->sync([$branch_id ?: 1]);
        });

        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('created_at', 'desc');
        });
    }

    public function fill(array $attributes)
    {
        return $this->_fill($attributes);
    }

    public function setStatusAttribute($value)
    {
        if (is_string($value)) {
            $status = OrderStatus::query()->where('slug', $value)->first();

            if ($status) {
                $this->attributes['status'] = $status->id;
            }
        } else {
            $this->attributes['status'] = $value;
        }
    }

    /**
     * Backward compatibility: Get the first shipment_id from order_shipments
     */
    public function getShipmentIdAttribute()
    {
        $firstShipment = $this->order_shipments()->first();
        return $firstShipment ? $firstShipment->shipment_id : null;
    }

    /**
     * Backward compatibility: Set shipment_id creates a new order_shipment record
     */
    public function setShipmentIdAttribute($value)
    {
        if ($value) {
            $this->order_shipments()->create([
                'shipment_id' => $value
            ]);
        }
    }

    public function getDeliveryStatusAttribute()
    {
        return mapOrderStatusToDeliveryStatus($this->order_status->slug);
    }

    public function status(): belongsTo
    {
        return $this->belongsTo(OrderStatus::class, 'status');
    }

    public function order_status(): belongsTo
    {
        return $this->belongsTo(OrderStatus::class, 'status');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class, 'shop_id');
    }

    public function coupon(): belongsTo
    {
        return $this->belongsTo(Coupon::class, 'coupon_id');
    }

    public function discounts(): BelongsToMany
    {
        return $this->belongsToMany(Discount::class, 'discount_usage')
            ->withPivot('customer_id', 'order_status')
            ->withTimestamps();
    }

    public function products(): HasMany
    {
        return $this->hasMany(OrderProduct::class);
    }

    public function payment_method()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function shipping_method()
    {
        return $this->belongsTo(Shipping::class, 'shipping_class_id')->withoutGlobalScope('group_scope');
    }

    public function wallet_transactions()
    {
        return $this->hasMany(WalletTransaction::class)->orderByRaw('created_at desc, id desc');
    }

    public function payments()
    {
        return $this->hasMany(PaymentTransaction::class)->orderByRaw('created_at desc, id desc');
    }

    public function success_payment()
    {
        return $this->hasOne(PaymentTransaction::class)->where('status', PaymentTransactionEnum::SUCCESS);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class)->orderByRaw('created_at desc, id desc');
    }

    public function shipments()
    {
        return $this->hasMany(Shipment::class)->orderByRaw('created_at desc, id desc');
    }

    public function order_shipments()
    {
        return $this->hasMany(OrderShipment::class)->orderByRaw('created_at desc, id desc');
    }

    public function credit_memos()
    {
        return $this->hasMany(CreditMemo::class)->orderByRaw('created_at desc, id desc');
    }

    public function history()
    {
        return $this->hasMany(OrderHistory::class)->orderByRaw('created_at desc, id desc');
    }

    public function tng_loyalty_point_history()
    {
        return $this->hasMany(TngLoyaltyPointHistory::class, 'order_id');
    }

    public function refund()
    {
        return $this->hasOne(Refund::class, 'order_id');
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(OrderAttribute::class, 'order_attribute_values')
            ->withPivot('value');
    }

    public function children()
    {
        return $this->hasMany(static::class, 'parent_id', 'id');
    }

    public function parent_order()
    {
        return $this->hasOne(static::class, 'id', 'parent_id');
    }

    public function getIsInvoicedAttribute()
    {
        return $this->invoices()->exists();
        // return $this->invoices()->exists() && $this->total_invoiced != null && $this->total_invoiced >= $this->paid_total;
    }
    public function getBillingAddressAttribute()
    {
        return $this->shipping_address;
    }

    public function getQtyOrderedAttribute()
    {
        return $this->products->sum('order_quantity');
    }

    public function getQtyShippedAttribute()
    {
        return $this->shipments->sum('items_qty');
    }

    public function getQtyRefundedAttribute()
    {
        return $this->products->sum('refunded_quantity');
    }

    public function getRefundedShippingAttribute()
    {
        return $this->credit_memos->where('status', CreditMemoEnum::SUCCESS)->sum('shipping_refund');
    }

    public function getCanCancelAttribute()
    {
        return $this->refund == null &&
            ($this->hasStatus(OrderEnum::PENDING) ||
                $this->hasStatus(OrderEnum::PROCESSING) ||
                $this->hasStatus(OrderEnum::PACKED)
            ) && $this->total_refunded <= 0;
    }

    public function hasStatus($slug)
    {
        $order_status = OrderStatus::firstWhere('slug', $slug);
        return $this->status == $order_status->id;
    }

    /**
     * Get all shipment IDs for this order
     */
    public function getShipmentIds()
    {
        return $this->order_shipments()->pluck('shipment_id')->toArray();
    }

    /**
     * Add a new shipment to this order
     */
    public function addShipment($shipmentId, $metadata = [])
    {
        return $this->order_shipments()->create([
            'shipment_id' => $shipmentId,
            'metadata' => $metadata
        ]);
    }

    /**
     * Check if order has any shipments
     */
    public function hasShipments()
    {
        return $this->order_shipments()->exists();
    }
}
