<?php

namespace App\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderShipment extends Model
{
    protected $table = 'order_shipments';

    public $guarded = [];

    protected $casts = [
        'metadata' => 'json',
    ];

    /**
     * Get the order that owns the shipment.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the tracking URL for this shipment.
     */
    public function getTrackingUrlAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // Generate tracking URL if not set
        if ($this->shipment_id) {
            return config('services.delyva.url') . '/order/' . $this->shipment_id . '/label?companyId=' . config('services.delyva.company_id');
        }

        return null;
    }
}
