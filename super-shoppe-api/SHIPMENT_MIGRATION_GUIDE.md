# Order Shipments Migration Guide

## Overview

This guide covers the migration from storing single `shipment_id` in DynamoDB to storing multiple shipment records in the `order_shipments` MySQL table.

## Database Changes

### New Table: `order_shipments`

```sql
CREATE TABLE order_shipments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    shipment_id VARCHAR(255) NOT NULL,
    tracking_url VARCHAR(255) NULL,
    metadata JSON NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_order_shipment (order_id, shipment_id)
);
```

## Migration Steps

### 1. Run Database Migration

```bash
cd super-shoppe-api
php artisan migrate
```

### 2. Migrate Data from DynamoDB

#### Test Migration (Dry Run)

```bash
php artisan shipments:migrate-from-ddb --dry-run
```

#### Run Actual Migration

```bash
php artisan shipments:migrate-from-ddb
```

#### Custom Options

```bash
# With custom batch size and delay
php artisan shipments:migrate-from-ddb --batch-size=25 --delay=1.0

# Available options:
# --batch-size=50    : Number of records per batch (default: 50)
# --delay=0.5        : Delay in seconds between DDB fetches (default: 0.5)
# --dry-run          : Test run without making changes
```

## Code Changes

### Order Model - New Methods

```php
// Get all shipment IDs for an order
$shipmentIds = $order->getShipmentIds();

// Add a new shipment
$order->addShipment('AWB123456', ['extra' => 'data']);

// Check if order has shipments
if ($order->hasShipments()) {
    // Handle shipments
}

// Access shipments relationship
$shipments = $order->order_shipments;
```

### DelyvaService - Updated Methods

```php
// Create shipment (now creates OrderShipment record)
$delyvaService->createShipment($order);

// Get AWB URLs
$urls = $delyvaService->getAirwayBillURL($order);
// Returns single URL string if one shipment, array if multiple

// Get specific shipment URL
$url = $delyvaService->getAirwayBillURL($order, 'specific_shipment_id');
```

## Backward Compatibility

The changes maintain backward compatibility:

### Accessing shipment_id

```php
// Still works - returns first shipment_id
$shipmentId = $order->shipment_id;

// Setting shipment_id creates new OrderShipment record
$order->shipment_id = 'AWB123456';
$order->save();
```

### Multiple Shipments

```php
// New way - access all shipments
foreach ($order->order_shipments as $shipment) {
    echo "AWB: " . $shipment->shipment_id;
    echo "URL: " . $shipment->tracking_url;
    echo "Created: " . $shipment->created_at;
}
```

## Testing

### Verify Migration

```php
// Check if data migrated correctly
$order = Order::find(123);
echo "Legacy shipment_id: " . $order->shipment_id; // First shipment
echo "All shipments: " . implode(', ', $order->getShipmentIds());
```

### Test New Functionality

```php
// Create multiple shipments for testing
$order->addShipment('AWB001');
$order->addShipment('AWB002');

// Verify URLs
$urls = app(DelyvaService::class)->getAirwayBillURL($order);
// Should return array with multiple URLs
```

## Rollback Plan

If rollback is needed:

1. The DynamoDB data remains untouched
2. Remove the `order_shipments` relationship from Order model
3. Restore `shipment_id` to `$fill_values` in Order model
4. Revert DelyvaService changes

## Monitoring

Monitor the migration:

-   Check Laravel logs for migration progress
-   Verify data integrity after migration
-   Test shipment creation with new orders
-   Ensure AWB URL generation works correctly

## Notes

-   DynamoDB data is preserved during migration
-   New shipments will only be stored in MySQL
-   The migration command handles DDB throttling automatically
-   All existing functionality continues to work unchanged
