Page({
  async onLoad(query) {
    if (query && query.route) {
      this.setData({
        targetUrl: this.data.targetUrl + query.route,
      });
    }
    this.webview = await my.createWebViewContext("webview");
  },
  data: {
    targetUrl: `https://empty-beatles-shares-camcorder.trycloudflare.com/`,
  },
  listen(req) {
    if (req.detail) {
      return this.handleMessage(req.detail);
    }
    this.webview.postMessage(req.detail);
  },
  handleMessage(data) {
    switch(data.type) {
      case "SS_USER_INFO":
        this.getUserInfo()
          .then((result) => {
            return this.webview.postMessage({
              type: 'SS_USER_INFO_COMPLETE',
              auth_code: result.authCode
            });
          })
          .catch((e) => {
            return this.webview.postMessage({
              type: 'SS_USER_INFO_COMPLETE',
              auth_code: null
            });
          });
        break;
      case "SS_PAYMENT":
        if(data.redirect) {
          this.pay(data.redirect)
            .then((result) => {
              return this.webview.postMessage({
                type: 'SS_PAYMENT_COMPLETE',
                resultCode: result.resultCode
              });
            })
            .catch((e) => {
              return this.webview.postMessage({
                type: 'SS_PAYMENT_COMPLETE',
                resultCode: null
              });
            });
        }
        break;
      case "SS_EVENT_TRACK":
        if(data.pageView) {
          if (data.additionalInfo) {
            my.tngdMPEventTagging({
              pageView: data.pageView,
              additionalInfo: data.additionalInfo
            });
          }
          else {
            my.tngdMPEventTagging({
              pageView: data.pageView
            });
          }
        }
        break;
    }
  },
  getUserInfo() {
    return new Promise((resolve, reject) => {
      my.getAuthCode({
        scopes: ['BASE_USER_INFO', 'USER_INFO', 'USER_ADDRESS'],
        success: resolve,
        fail: reject,
      });
    });
  },
  pay(paymentUrl) {
    return new Promise((resolve, reject) => {
      my.tradePay({
        paymentUrl,
        success: resolve,
        fail: reject,
      });
    });
  }
});
